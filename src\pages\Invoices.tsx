import React, { useState, useMemo } from 'react'
import { Plus, FileText, Edit, Trash2, Eye, Check, X, Search, Download } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DocumentManager } from '@/components/documents/DocumentManager'
import { InvoiceDetailsModal } from '@/components/invoices/InvoiceDetailsModal'
import { useToast } from '@/hooks/use-toast'
import { useInvoices, useActiveCustomers, useActiveAccounts, useDeleteInvoice, useUpdateInvoiceStatus } from '@/hooks/queries'
import { useQueryClient } from '@tanstack/react-query'
import { queryKeys } from '@/hooks/queries/queryKeys'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/hooks/useAuthHook'
import { InvoiceForm } from '@/components/invoices/InvoiceForm'
import { InvoiceExportDialog } from '@/components/invoices/InvoiceExportDialog'
import { LoadingPage } from '@/components/ui/loading'
import type { InvoiceWithCustomer, InvoiceFormData, InvoiceLineData } from '@/types/invoices'
import type { Customer, Account, InvoiceStatus } from '@/types/database'

export function Invoices() {
  const { data: invoices = [], isLoading: loading } = useInvoices()
  const { data: customers = [] } = useActiveCustomers()
  const { data: accounts = [] } = useActiveAccounts()
  const deleteInvoice = useDeleteInvoice()
  const updateInvoiceStatusMutation = useUpdateInvoiceStatus()
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceWithCustomer | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<InvoiceWithCustomer | null>(null)
  const [viewingInvoiceId, setViewingInvoiceId] = useState<string | null>(null)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const { toast } = useToast()
  const { profile } = useAuth()

  // React Query handles data fetching automatically

  const generateInvoiceNumber = () => {
    const date = new Date()
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const timestamp = Date.now().toString().slice(-4)
    return `INV-${year}${month}-${timestamp}`
  }

  const handleSubmit = async (formData: InvoiceFormData, invoiceLines: InvoiceLineData[]) => {
    if (!formData.customer_id || !formData.account_id || invoiceLines.length === 0) {
      toast({
        title: "Error",
        description: "Please select a customer, account, and add at least one line item",
        variant: "destructive",
      })
      return
    }

    try {
      const totals = invoiceLines.reduce((acc, line) => {
        const lineTotal = line.quantity * line.unit_price
        const lineTax = lineTotal * (line.tax_rate_pct / 100)
        return {
          subtotal: acc.subtotal + lineTotal,
          taxAmount: acc.taxAmount + lineTax
        }
      }, { subtotal: 0, taxAmount: 0 })

      // Exclude account_id from invoice data (it belongs to invoice_lines)
      const { account_id, ...invoiceFormFields } = formData
      const invoiceData = {
        ...invoiceFormFields,
        customer_id: formData.customer_id,
        invoice_number: formData.invoice_number || generateInvoiceNumber(),
        total_amount: totals.subtotal + totals.taxAmount,
        tax_amount: totals.taxAmount,
        org_id: profile?.org_id,
        status: formData.status || 'draft',
        date_issued: formData.date_issued,
        due_date: formData.due_date,
      }

      let invoiceId: string

      if (editingInvoice) {
        const { error } = await supabase
          .from('invoices')
          .update(invoiceData)
          .eq('id', editingInvoice.id)

        if (error) throw error
        invoiceId = editingInvoice.id

        // Delete existing lines
        await supabase
          .from('invoice_lines')
          .delete()
          .eq('invoice_id', invoiceId)
      } else {
        const { data, error } = await supabase
          .from('invoices')
          .insert([invoiceData])
          .select()
          .single()

        if (error) throw error
        if (!data) throw new Error('No data returned from insert')
        invoiceId = data.id
      }

      // Insert invoice lines
      const linesData = invoiceLines.map(line => ({
        invoice_id: invoiceId,
        account_id: formData.account_id && formData.account_id !== '' ? formData.account_id : null, // Use form-level account_id
        description: `${line.item}${line.description ? ' - ' + line.description : ''}`, // Combine item and description
        quantity: line.quantity,
        unit_price: line.unit_price,
        tax_rate_pct: line.tax_rate_pct
      }))

      const { error: linesError } = await supabase
        .from('invoice_lines')
        .insert(linesData)

      if (linesError) throw linesError

      toast({
        title: "Success",
        description: editingInvoice ? "Invoice updated successfully" : "Invoice created successfully",
      })

      setIsDialogOpen(false)
      setEditingInvoice(null)

      // Invalidate and refetch invoices data
      queryClient.invalidateQueries({
        queryKey: queryKeys.invoices.all(profile?.org_id || '')
      })
    } catch (error) {
      console.error('Error saving invoice:', error)
      toast({
        title: "Error",
        description: "Failed to save invoice",
        variant: "destructive",
      })
      throw error
    }
  }

  const updateInvoiceStatus = async (invoice: InvoiceWithCustomer, newStatus: InvoiceStatus) => {
    try {
      await updateInvoiceStatusMutation.mutateAsync({
        invoiceId: invoice.id,
        status: newStatus
      })
    } catch (error) {
      console.error('Error updating invoice status:', error)
      // Error handling is done by the React Query hook
    }
  }

  const handleDelete = async (invoice: InvoiceWithCustomer) => {
    if (!confirm(`Are you sure you want to delete invoice "${invoice.invoice_number}"? This action cannot be undone.`)) return

    try {
      await deleteInvoice.mutateAsync(invoice.id)
    } catch (error) {
      console.error('Error deleting invoice:', error)
      // Error handling is done by the React Query hook
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'sent': return 'bg-blue-100 text-blue-800'
      case 'paid': return 'bg-green-100 text-green-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleEdit = (invoice: InvoiceWithCustomer) => {
    setEditingInvoice(invoice)
    setIsDialogOpen(true)
  }

  const openCreateDialog = () => {
    setEditingInvoice(null)
    setIsDialogOpen(true)
  }

  const handleViewInvoice = (invoiceId: string) => {
    setViewingInvoiceId(invoiceId)
    setIsViewModalOpen(true)
  }

  // Filter invoices based on search term
  const filteredInvoices = useMemo(() => {
    if (!searchTerm) return invoices

    const searchLower = searchTerm.toLowerCase()
    return invoices.filter(invoice =>
      invoice.invoice_number.toLowerCase().includes(searchLower) ||
      invoice.customers?.name?.toLowerCase().includes(searchLower)
    )
  }, [invoices, searchTerm])

  if (loading) {
    return <LoadingPage text="Loading invoices..." fullScreen={false} />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Invoices</h1>
          <p className="text-gray-600">Create and manage sales invoices</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search invoices..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 w-64"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setIsExportDialogOpen(true)}
            disabled={filteredInvoices.length === 0}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Create Invoice
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Invoices ({filteredInvoices.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice #</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">{invoice.invoice_number}</TableCell>
                  <TableCell>{invoice.customers?.name || 'Unknown'}</TableCell>
                  <TableCell>{new Date(invoice.date_issued).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(invoice.due_date).toLocaleDateString()}</TableCell>
                  <TableCell>UGX {invoice.total_amount.toLocaleString()}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(invoice.status)}>
                      {invoice.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewInvoice(invoice.id)}
                        title="View invoice details"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(invoice)}
                        title="Edit invoice"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      {invoice.status === 'draft' && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => updateInvoiceStatus(invoice, 'sent')}
                          title="Mark as Sent"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                      )}
                      {invoice.status === 'sent' && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => updateInvoiceStatus(invoice, 'paid')}
                          title="Mark as Paid"
                          className="text-green-600 hover:text-green-700"
                        >
                          <Check className="h-4 w-4" />
                        </Button>
                      )}
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedInvoice(invoice)}
                            title="Manage invoice documents"
                          >
                            <FileText className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl">
                          <DialogHeader>
                            <DialogTitle>
                              Invoice Documents - {invoice.invoice_number}
                            </DialogTitle>
                          </DialogHeader>
                          {selectedInvoice && (
                            <DocumentManager
                              attachedToType="invoice"
                              attachedToId={selectedInvoice.id}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(invoice)}
                        title="Delete Invoice"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <InvoiceForm
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        editingInvoice={editingInvoice}
        customers={customers}
        accounts={accounts}
        onSubmit={handleSubmit}
      />

      {/* Invoice Details Modal */}
      {viewingInvoiceId && (
        <InvoiceDetailsModal
          open={isViewModalOpen}
          onOpenChange={setIsViewModalOpen}
          invoiceId={viewingInvoiceId}
        />
      )}

      {/* Invoice Export Dialog */}
      <InvoiceExportDialog
        open={isExportDialogOpen}
        onOpenChange={setIsExportDialogOpen}
        customers={customers}
        totalInvoices={filteredInvoices.length}
      />
    </div>
  )
}
